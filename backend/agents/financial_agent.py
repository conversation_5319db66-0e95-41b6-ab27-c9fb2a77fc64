"""
财务 Agent - 负责财务分析和记账凭证生成
"""
from typing import Dict, Any, List, Optional
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from datetime import datetime
import json
import re

from config.settings import settings
from models.schemas import AccountingEntry, VoucherCreate, AgentType
from utils.accounting_tools import AccountingTools


class FinancialAgent:
    """财务分析和凭证生成 Agent"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.default_model,
            temperature=0.1,
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url
        )
        self.accounting_tools = AccountingTools()
        self.system_prompt = self._get_system_prompt()
    
    def _get_system_prompt(self) -> str:
        """获取系统提示"""
        return """
你是一个专业的财务会计AI助手，专门负责分析业务交易并生成准确的记账凭证。

你的主要职责：
1. 分析用户描述的业务交易
2. 识别涉及的会计科目
3. 确定借贷方向和金额
4. 生成符合会计准则的记账凭证
5. 提供清晰的财务解释

工作原则：
- 严格遵循复式记账原理（借贷必相等）
- 使用标准的会计科目代码
- 确保凭证的完整性和准确性
- 提供详细的业务分析和解释
- 如有疑问，主动询问澄清

会计科目参考（部分）：
- 1001 库存现金
- 1002 银行存款
- 1122 应收账款
- 1403 原材料
- 1405 库存商品
- 2202 应付账款
- 4001 主营业务收入
- 5401 主营业务成本
- 6601 销售费用
- 6602 管理费用

请始终以专业、准确、负责的态度处理每一笔业务。
"""

    async def process_message(self, message: str) -> str:
        """处理用户消息的通用方法"""
        try:
            # 使用分析交易的方法来处理消息
            response = await self.analyze_transaction(message)
            return response
        except Exception as e:
            return f"抱歉，处理您的消息时出现错误：{str(e)}"

    async def analyze_transaction(
        self,
        user_input: str,
        memory_context: Optional[str] = None,
        previous_feedback: Optional[str] = None
    ) -> str:
        """分析业务交易"""
        
        # 构建分析提示
        analysis_prompt = ChatPromptTemplate.from_messages([
            ("system", self.system_prompt),
            ("human", """
请分析以下业务交易：

业务描述：{user_input}

{memory_context}

{previous_feedback}

请提供详细的财务分析，包括：
1. 业务性质识别
2. 涉及的会计要素
3. 影响的会计科目
4. 借贷方向分析
5. 金额确定依据
6. 相关会计准则

分析结果请以结构化的方式呈现。
""")
        ])
        
        # 准备上下文
        context_parts = []
        if memory_context:
            context_parts.append(f"历史上下文：\n{memory_context}")
        if previous_feedback:
            context_parts.append(f"之前的反馈：\n{previous_feedback}")
        
        context_str = "\n\n".join(context_parts) if context_parts else ""
        
        # 调用 LLM
        messages = analysis_prompt.format_messages(
            user_input=user_input,
            memory_context=context_str,
            previous_feedback=""
        )
        
        response = await self.llm.ainvoke(messages)
        return response.content
    
    async def generate_voucher(
        self,
        analysis_result: str,
        conversation_id: str
    ) -> Dict[str, Any]:
        """基于分析结果生成记账凭证"""
        
        voucher_prompt = ChatPromptTemplate.from_messages([
            ("system", self.system_prompt),
            ("human", """
基于以下财务分析结果，生成标准的记账凭证：

分析结果：
{analysis_result}

请生成JSON格式的记账凭证，包含以下字段：
{{
    "voucher_date": "YYYY-MM-DD",
    "description": "凭证摘要",
    "entries": [
        {{
            "account_code": "科目代码",
            "account_name": "科目名称", 
            "debit_amount": 借方金额或null,
            "credit_amount": 贷方金额或null,
            "description": "分录摘要"
        }}
    ]
}}

要求：
1. 确保借贷平衡
2. 使用标准会计科目
3. 金额精确到分
4. 摘要清晰明确
5. 至少包含两个分录

请只返回JSON格式的凭证数据，不要包含其他文字。
""")
        ])
        
        messages = voucher_prompt.format_messages(
            analysis_result=analysis_result
        )
        
        response = await self.llm.ainvoke(messages)
        
        try:
            # 解析 JSON 响应
            voucher_data = json.loads(response.content)
            
            # 验证凭证数据
            validated_voucher = self._validate_voucher(voucher_data)
            
            return validated_voucher
            
        except json.JSONDecodeError as e:
            # 如果 JSON 解析失败，尝试提取 JSON 部分
            json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
            if json_match:
                try:
                    voucher_data = json.loads(json_match.group())
                    return self._validate_voucher(voucher_data)
                except:
                    pass
            
            raise ValueError(f"无法解析凭证数据: {e}")
    
    def _validate_voucher(self, voucher_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证凭证数据"""
        # 检查必要字段
        required_fields = ["voucher_date", "description", "entries"]
        for field in required_fields:
            if field not in voucher_data:
                raise ValueError(f"缺少必要字段: {field}")
        
        # 验证分录
        entries = voucher_data["entries"]
        if len(entries) < 2:
            raise ValueError("至少需要两个会计分录")
        
        total_debit = 0
        total_credit = 0
        
        for entry in entries:
            # 检查分录字段
            required_entry_fields = ["account_code", "account_name"]
            for field in required_entry_fields:
                if field not in entry:
                    raise ValueError(f"分录缺少必要字段: {field}")
            
            # 计算借贷总额
            debit = entry.get("debit_amount", 0) or 0
            credit = entry.get("credit_amount", 0) or 0
            
            total_debit += debit
            total_credit += credit
            
            # 确保每个分录有且仅有借方或贷方金额
            if (debit > 0 and credit > 0) or (debit == 0 and credit == 0):
                raise ValueError(f"分录 {entry['account_name']} 的借贷金额设置不正确")
        
        # 检查借贷平衡
        if abs(total_debit - total_credit) > 0.01:  # 允许1分的误差
            raise ValueError(f"借贷不平衡: 借方总额 {total_debit}, 贷方总额 {total_credit}")
        
        # 添加总额信息
        voucher_data["total_debit"] = total_debit
        voucher_data["total_credit"] = total_credit
        
        return voucher_data
    
    async def finalize_voucher(
        self,
        voucher_data: Dict[str, Any],
        audit_feedback: Optional[str] = None,
        human_feedback: Optional[str] = None
    ) -> Dict[str, Any]:
        """最终确认凭证"""
        
        # 如果有反馈，进行相应调整
        if audit_feedback or human_feedback:
            adjustment_prompt = ChatPromptTemplate.from_messages([
                ("system", self.system_prompt),
                ("human", """
请根据以下反馈调整记账凭证：

原凭证：
{voucher_data}

审计反馈：
{audit_feedback}

人工反馈：
{human_feedback}

请返回调整后的JSON格式凭证数据。
""")
            ])
            
            messages = adjustment_prompt.format_messages(
                voucher_data=json.dumps(voucher_data, ensure_ascii=False, indent=2),
                audit_feedback=audit_feedback or "无",
                human_feedback=human_feedback or "无"
            )
            
            response = await self.llm.ainvoke(messages)
            
            try:
                adjusted_voucher = json.loads(response.content)
                return self._validate_voucher(adjusted_voucher)
            except:
                # 如果调整失败，返回原凭证
                pass
        
        # 添加最终确认信息
        voucher_data["finalized_at"] = datetime.now().isoformat()
        voucher_data["status"] = "approved"
        
        return voucher_data
    
    async def explain_voucher(self, voucher_data: Dict[str, Any]) -> str:
        """解释凭证内容"""
        
        explanation_prompt = ChatPromptTemplate.from_messages([
            ("system", self.system_prompt),
            ("human", """
请详细解释以下记账凭证的会计处理逻辑：

{voucher_data}

请从以下角度进行解释：
1. 业务背景和性质
2. 会计科目选择理由
3. 借贷方向的依据
4. 对财务状况的影响
5. 相关会计准则

请用通俗易懂的语言解释，便于非专业人士理解。
""")
        ])
        
        messages = explanation_prompt.format_messages(
            voucher_data=json.dumps(voucher_data, ensure_ascii=False, indent=2)
        )
        
        response = await self.llm.ainvoke(messages)
        return response.content
