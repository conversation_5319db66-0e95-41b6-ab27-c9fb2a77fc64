"""
CopilotKit API 端点
"""
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import StreamingResponse
import json
import asyncio
from typing import Dict, Any, List
from loguru import logger

from services.agent_service import AgentService
from services.conversation_service import ConversationService

router = APIRouter()

@router.post("/copilot")
async def copilot_endpoint(request: Request):
    """
    CopilotKit 运行时端点
    处理来自 CopilotKit 前端的请求
    """
    try:
        # 获取请求体
        body = await request.json()
        logger.info(f"CopilotKit request: {body}")
        
        # 检查请求类型
        if "messages" in body:
            # 这是一个聊天请求
            return await handle_chat_request(body)
        elif "action" in body:
            # 这是一个动作请求
            return await handle_action_request(body)
        else:
            # 未知请求类型
            logger.warning(f"Unknown CopilotKit request type: {body}")
            return {"error": "Unknown request type"}
            
    except Exception as e:
        logger.error(f"CopilotKit endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def handle_chat_request(body: Dict[str, Any]):
    """处理聊天请求"""
    try:
        messages = body.get("messages", [])
        if not messages:
            return {"error": "No messages provided"}
        
        # 获取最后一条用户消息
        last_message = messages[-1]
        user_content = last_message.get("content", "")
        
        # 创建流式响应
        async def generate_response():
            try:
                # 暂时返回一个简单的响应，避免 OpenAI API 调用
                simple_response = f"您好！我是您的财务AI助手。您提到：「{user_content}」\n\n我可以帮助您：\n1. 分析业务交易\n2. 生成记账凭证\n3. 审核财务数据\n4. 提供会计建议\n\n请告诉我您需要处理什么具体的财务业务？"

                # 构造 CopilotKit 格式的响应
                copilot_response = {
                    "id": f"msg_{len(messages)}",
                    "role": "assistant",
                    "content": simple_response,
                    "function_call": None
                }

                # 流式返回响应
                yield f"data: {json.dumps(copilot_response, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"

            except Exception as e:
                logger.error(f"Error generating response: {e}")
                error_response = {
                    "id": f"msg_{len(messages)}",
                    "role": "assistant",
                    "content": f"抱歉，处理您的请求时出现错误：{str(e)}",
                    "function_call": None
                }
                yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "*",
            }
        )
        
    except Exception as e:
        logger.error(f"Error handling chat request: {e}")
        return {"error": str(e)}


async def handle_action_request(body: Dict[str, Any]):
    """处理动作请求"""
    try:
        action = body.get("action", {})
        action_name = action.get("name", "")
        parameters = action.get("parameters", {})
        
        logger.info(f"Handling action: {action_name} with parameters: {parameters}")
        
        if action_name == "sendMessage":
            # 处理发送消息动作
            message = parameters.get("message", "")
            agent_type = parameters.get("agentType", "financial")
            
            agent_service = AgentService()
            response = await agent_service.process_message(
                message=message,
                agent_type=agent_type
            )
            
            return {
                "success": True,
                "result": response.get("content", "消息已处理")
            }
            
        elif action_name == "submitFeedback":
            # 处理提交反馈动作
            feedback = parameters.get("feedback", "")
            rating = parameters.get("rating", 5)
            
            # 这里可以添加反馈处理逻辑
            logger.info(f"Received feedback: {feedback}, rating: {rating}")
            
            return {
                "success": True,
                "result": "反馈已提交，谢谢您的意见！"
            }
            
        else:
            logger.warning(f"Unknown action: {action_name}")
            return {
                "success": False,
                "error": f"Unknown action: {action_name}"
            }
            
    except Exception as e:
        logger.error(f"Error handling action request: {e}")
        return {
            "success": False,
            "error": str(e)
        }
