"""
FastAPI 主应用入口
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import logging
from loguru import logger

from config.settings import settings
from app.api import router as api_router
from app.api.copilot import router as copilot_router
from app.database import init_db


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("启动财务会计AI Agent应用...")
    
    # 初始化数据库
    await init_db()
    
    # 初始化其他服务
    logger.info("应用启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("关闭应用...")


# 创建 FastAPI 应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="基于 LangGraph 的财务会计记账凭证自动生成 AI Agent 工具",
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加受信任主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.debug else ["localhost", "127.0.0.1"]
)

# 注册路由
app.include_router(api_router, prefix="/api/v1")
app.include_router(copilot_router, prefix="/api")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "财务会计记账凭证AI Agent API",
        "version": settings.app_version,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    
    # 配置日志
    logging.basicConfig(level=getattr(logging, settings.log_level))
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
