import React, { useState, useRef, useEffect } from 'react';
import { Input, Button, List, Avatar, Typography, Space, Tag } from 'antd';
import { SendOutlined, UserOutlined, RobotOutlined } from '@ant-design/icons';
import type { Conversation, MessageCreate, AgentType } from '../../types';
import { MessageRole } from '../../types';

const { TextArea } = Input;
const { Text } = Typography;

interface ChatInterfaceProps {
  conversation: Conversation | null;
  onSendMessage: (message: MessageCreate) => Promise<void>;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  conversation,
  onSendMessage
}) => {
  const [inputValue, setInputValue] = useState('');
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [conversation?.messages]);

  const handleSend = async () => {
    if (!inputValue.trim() || !conversation || sending) return;

    const message: MessageCreate = {
      content: inputValue.trim(),
      role: MessageRole.USER
    };

    setSending(true);
    try {
      await onSendMessage(message);
      setInputValue('');
    } catch (error) {
      console.error('发送消息失败:', error);
    } finally {
      setSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const getAgentIcon = (agentType?: AgentType) => {
    switch (agentType) {
      case 'financial':
        return <RobotOutlined style={{ color: '#1890ff' }} />;
      case 'audit':
        return <RobotOutlined style={{ color: '#52c41a' }} />;
      default:
        return <RobotOutlined />;
    }
  };

  const getAgentTag = (agentType?: AgentType) => {
    switch (agentType) {
      case 'financial':
        return <Tag color="blue">财务AI</Tag>;
      case 'audit':
        return <Tag color="green">审计AI</Tag>;
      default:
        return null;
    }
  };

  if (!conversation) {
    return (
      <div style={{ 
        height: '100%', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        color: '#999'
      }}>
        请选择或创建一个对话
      </div>
    );
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 消息列表 */}
      <div style={{ 
        flex: 1, 
        overflow: 'auto', 
        padding: '16px',
        backgroundColor: '#fafafa'
      }}>
        {conversation.messages.length === 0 ? (
          <div style={{ 
            textAlign: 'center', 
            color: '#999', 
            marginTop: '50px' 
          }}>
            开始对话吧！描述您需要处理的业务交易。
          </div>
        ) : (
          <List
            dataSource={conversation.messages}
            renderItem={(message) => (
              <List.Item style={{ border: 'none', padding: '8px 0' }}>
                <div style={{ 
                  width: '100%',
                  display: 'flex',
                  justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start'
                }}>
                  <div style={{
                    maxWidth: '80%',
                    display: 'flex',
                    flexDirection: message.role === 'user' ? 'row-reverse' : 'row',
                    alignItems: 'flex-start',
                    gap: '8px'
                  }}>
                    <Avatar 
                      icon={message.role === 'user' ? <UserOutlined /> : getAgentIcon(message.agent_type)}
                      style={{ 
                        backgroundColor: message.role === 'user' ? '#1890ff' : '#52c41a',
                        flexShrink: 0
                      }}
                    />
                    <div style={{
                      backgroundColor: message.role === 'user' ? '#e6f7ff' : '#f6ffed',
                      padding: '12px 16px',
                      borderRadius: '12px',
                      border: '1px solid ' + (message.role === 'user' ? '#91d5ff' : '#b7eb8f')
                    }}>
                      {message.agent_type && (
                        <div style={{ marginBottom: '4px' }}>
                          {getAgentTag(message.agent_type)}
                        </div>
                      )}
                      <Text style={{ whiteSpace: 'pre-wrap' }}>
                        {message.content}
                      </Text>
                      <div style={{ 
                        marginTop: '8px', 
                        fontSize: '12px', 
                        color: '#999',
                        textAlign: message.role === 'user' ? 'right' : 'left'
                      }}>
                        {new Date(message.created_at || '').toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                </div>
              </List.Item>
            )}
          />
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div style={{ 
        padding: '16px', 
        borderTop: '1px solid #f0f0f0',
        backgroundColor: '#fff'
      }}>
        <Space.Compact style={{ width: '100%' }}>
          <TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="描述您的业务交易，例如：收到客户货款10000元..."
            autoSize={{ minRows: 1, maxRows: 4 }}
            disabled={sending}
            style={{ resize: 'none' }}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSend}
            loading={sending}
            disabled={!inputValue.trim()}
          >
            发送
          </Button>
        </Space.Compact>
      </div>
    </div>
  );
};

export default ChatInterface;
