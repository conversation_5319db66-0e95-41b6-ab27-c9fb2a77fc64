/* App 组件样式 */
.App {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* CopilotKit 样式覆盖 */
.copilot-sidebar {
  z-index: 1000;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .App {
    padding: 0;
  }
}

/* 加载动画 */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 错误状态 */
.error-boundary {
  padding: 20px;
  text-align: center;
  color: #ff4d4f;
}

/* 主题过渡 */
.App * {
  transition: background-color 0.3s ease, color 0.3s ease;
}
