yarn run v1.22.22
$ react-scripts start
(node:146176) [DEP_WEBPACK_DEV_SERVER_ON_AFTER_SETUP_MIDDLEWARE] DeprecationWarning: 'onAfterSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:146176) [DEP_WEBPACK_DEV_SERVER_ON_BEFORE_SETUP_MIDDLEWARE] DeprecationWarning: 'onBeforeSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
Starting the development server...

Compiled with warnings.

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/internal/fetchOptions.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/internal/fetchOptions.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/internal/fetchSource.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/internal/fetchSource.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/error.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/error.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/hash.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/hash.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/request.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/request.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/result.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/result.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/variables.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/variables.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/client.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/client.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/cache.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/cache.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/compose.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/compose.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/debug.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/debug.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/fallback.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/fallback.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/fetch.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/fetch.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/map.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/map.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/ssr.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/ssr.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/subscription.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/subscription.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/gql.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/gql.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/collectTypenames.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/collectTypenames.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/formatDocument.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/formatDocument.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/index.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/index.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/operation.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/operation.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/streamUtils.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/streamUtils.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/error.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/error.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/helpers.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/helpers.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/kind.js' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/kind.js'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/parser.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/parser.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/printer.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/printer.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/values.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/values.ts'

Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/visitor.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/visitor.ts'

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in ./node_modules/@urql/core/dist/urql-core-chunk.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/internal/fetchOptions.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/internal/fetchOptions.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core-chunk.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/internal/fetchSource.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/internal/fetchSource.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core-chunk.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/error.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/error.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core-chunk.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/hash.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/hash.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core-chunk.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/request.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/request.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core-chunk.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/result.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/result.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core-chunk.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/variables.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/variables.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/client.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/client.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/cache.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/cache.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/compose.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/compose.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/debug.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/debug.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/fallback.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/fallback.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/fetch.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/fetch.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/map.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/map.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/ssr.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/ssr.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/subscription.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/exchanges/subscription.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/gql.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/gql.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/collectTypenames.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/collectTypenames.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/formatDocument.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/formatDocument.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/index.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/index.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/operation.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/operation.ts'

WARNING in ./node_modules/@urql/core/dist/urql-core.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/streamUtils.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@urql/core/src/utils/streamUtils.ts'

WARNING in ./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/error.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/error.ts'

WARNING in ./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/helpers.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/helpers.ts'

WARNING in ./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/kind.js' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/kind.js'

WARNING in ./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/parser.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/parser.ts'

WARNING in ./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/printer.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/printer.ts'

WARNING in ./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/values.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/values.ts'

WARNING in ./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs
Module Warning (from ./node_modules/source-map-loader/dist/cjs.js):
Failed to parse source map from '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/visitor.ts' file: Error: ENOENT: no such file or directory, open '/media/shun/bigdata/Projects/app365/account_3/frontend/node_modules/@0no-co/graphql.web/src/visitor.ts'

webpack compiled with 29 warnings
Files successfully emitted, waiting for typecheck results...
Issues checking in progress...
(node:146176) [DEP0060] DeprecationWarning: The `util._extend` API is deprecated. Please use Object.assign() instead.
No issues found.
