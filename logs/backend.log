INFO:     Will watch for changes in these directories: ['/media/shun/bigdata/Projects/app365/account_3/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [105827] using WatchFiles
INFO:     Started server process [105830]
INFO:     Waiting for application startup.
2025-07-22 21:34:26.756 | INFO     | app.main:lifespan:20 - 启动财务会计AI Agent应用...
2025-07-22 21:34:26.757 | INFO     | app.main:lifespan:26 - 应用启动完成
INFO:     Application startup complete.
Redis 连接失败: Error 111 connecting to localhost:6379. Connection refused.
INFO:     127.0.0.1:39354 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /manifest.json HTTP/1.1" 404 Not Found
