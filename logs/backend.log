INFO:     Will watch for changes in these directories: ['/media/shun/bigdata/Projects/app365/account_3/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [146103] using WatchFiles
INFO:     Started server process [146106]
INFO:     Waiting for application startup.
2025-07-22 22:10:05.509 | INFO     | app.main:lifespan:21 - 启动财务会计AI Agent应用...
2025-07-22 22:10:05.510 | INFO     | app.main:lifespan:27 - 应用启动完成
INFO:     Application startup complete.
Redis 连接失败: Error 111 connecting to localhost:6379. Connection refused.
INFO:     127.0.0.1:46678 - "GET /health HTTP/1.1" 200 OK
2025-07-22 22:10:11.127 | INFO     | app.api.copilot:copilot_endpoint:25 - CopilotKit request: {'operationName': 'availableAgents', 'query': 'query availableAgents {\n  availableAgents {\n    agents {\n      name\n      id\n      description\n      __typename\n    }\n    __typename\n  }\n}', 'variables': {}}
2025-07-22 22:10:11.128 | WARNING  | app.api.copilot:copilot_endpoint:36 - Unknown CopilotKit request type: {'operationName': 'availableAgents', 'query': 'query availableAgents {\n  availableAgents {\n    agents {\n      name\n      id\n      description\n      __typename\n    }\n    __typename\n  }\n}', 'variables': {}}
INFO:     127.0.0.1:0 - "POST /api/copilot HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/v1/conversations/?skip=0&limit=20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/v1/conversations/?skip=0&limit=20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/v1/conversations/?skip=0&limit=20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:0 - "GET /api/v1/conversations/?skip=0&limit=20 HTTP/1.1" 200 OK
